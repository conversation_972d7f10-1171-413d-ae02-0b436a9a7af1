/* Circular headshot with background removal effect */
.headshot-container {
  position: relative;
  width: 192px;
  height: 192px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.headshot-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  /* Background removal effect using CSS filters */
  filter: contrast(1.1) saturate(1.2) brightness(1.05);
  background: radial-gradient(circle, transparent 60%, rgba(255, 255, 255, 0.1) 100%);
}

/* Orbital animation keyframes */
@keyframes orbit-clockwise {
  from {
    transform: rotate(0deg) translateX(300px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(300px) rotate(-360deg);
  }
}

@keyframes orbit-counterclockwise {
  from {
    transform: rotate(0deg) translateX(300px) rotate(0deg);
  }
  to {
    transform: rotate(-360deg) translateX(300px) rotate(360deg);
  }
}

/* Connection line animation */
@keyframes connection-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleY(1.1);
  }
}

.connection-line {
  animation: connection-pulse 2s ease-in-out infinite;
}

/* Persona bubble hover effects */
.persona-bubble {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.persona-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Particle animation */
@keyframes particle-float {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-40px) rotate(360deg);
    opacity: 0;
  }
}

.particle {
  animation: particle-float 3s ease-out infinite;
}

/* Glow effect for active persona */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.6);
  }
}

.active-glow {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .headshot-container {
    width: 128px;
    height: 128px;
  }
  
  .persona-bubble {
    transform: scale(0.8);
  }
}

/* Smooth transitions for all elements */
* {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
