'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import '../../styles/persona-bubbles.css';

interface PersonaBubblesProps {
  gameMode: boolean;
  onDiscoverElement: (elementId: string) => void;
  discoveredElements: string[];
}

const personas = [
  {
    id: 'it-support',
    icon: '💻',
    title: 'IT Support Specialist',
    description: 'Network troubleshooting & system optimization',
    shortDesc: 'IT Support',
    href: '/projects',
    color: 'from-blue-500 to-cyan-500',
    position: 0, // Top position
    particles: ['🔧', '🖥️', '🔌', '📡']
  },
  {
    id: 'automation',
    icon: '🤖',
    title: 'Automation Engineer',
    description: 'Scripts & workflows that streamline operations',
    shortDesc: 'Automation',
    href: '/about',
    color: 'from-purple-500 to-pink-500',
    position: 1, // Middle position
    particles: ['⚡', '🔄', '📊', '🎯']
  },
  {
    id: 'creative',
    icon: '🎨',
    title: 'Creative Technologist',
    description: 'Art meets technology for innovative solutions',
    shortDesc: 'Creative Tech',
    href: '/about',
    color: 'from-orange-500 to-red-500',
    position: 2, // Bottom position
    particles: ['🎨', '✨', '🌈', '💫']
  }
];

export function PersonaBubbles({ gameMode, onDiscoverElement, discoveredElements }: PersonaBubblesProps) {
  const [hoveredPersona, setHoveredPersona] = useState<string | null>(null);
  const [activePersona, setActivePersona] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [particles, setParticles] = useState<Array<{ id: string; x: number; y: number; emoji: string }>>([]);

  // Auto-cycle through personas every 20 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!hoveredPersona) { // Only auto-cycle if not hovering
        setIsAnimating(true);
        setTimeout(() => {
          setActivePersona(prev => (prev + 1) % personas.length);
          setIsAnimating(false);
        }, 1000);
      }
    }, 20000);

    return () => clearInterval(interval);
  }, [hoveredPersona]);

  // Generate particles for hover effects
  useEffect(() => {
    if (hoveredPersona) {
      const persona = personas.find(p => p.id === hoveredPersona);
      if (persona) {
        const newParticles = Array.from({ length: 6 }, (_, i) => ({
          id: `${hoveredPersona}-${i}`,
          x: Math.random() * 100,
          y: Math.random() * 100,
          emoji: persona.particles[Math.floor(Math.random() * persona.particles.length)]
        }));
        setParticles(newParticles);
      }
    } else {
      setParticles([]);
    }
  }, [hoveredPersona]);

  const handlePersonaClick = (persona: typeof personas[0]) => {
    if (gameMode) {
      onDiscoverElement(`persona-click-${persona.id}`);
    }
    window.location.href = persona.href;
  };

  return (
    <div className="relative w-full h-screen flex items-center justify-center overflow-hidden">
      {/* Central headshot */}
      <div className="relative z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1 }}
          className="headshot-container"
        >
          <Image
            src="/ghazal-headshot.png"
            alt="Ghazal Erfani"
            width={192}
            height={192}
            className="headshot-image"
          />
        </motion.div>

        {/* Connection lines to active persona */}
        <motion.div
          className="connection-line absolute top-1/2 left-1/2 w-1 bg-gradient-to-r from-transparent via-white/50 to-transparent"
          style={{
            height: '200px',
            transformOrigin: 'top center',
            transform: `translate(-50%, -50%) rotate(${activePersona * 120}deg)`
          }}
          animate={{
            rotate: activePersona * 120
          }}
          transition={{ duration: 1, ease: "easeInOut" }}
        />
      </div>

      {/* Orbiting personas */}
      {personas.map((persona, index) => {
        const angle = (index * 120) - 90; // Start from top, 120 degrees apart
        const radius = 300;
        const x = Math.cos((angle * Math.PI) / 180) * radius;
        const y = Math.sin((angle * Math.PI) / 180) * radius;

        const isActive = index === activePersona;
        const isHovered = hoveredPersona === persona.id;

        return (
          <motion.div
            key={persona.id}
            className="absolute"
            style={{
              left: '50%',
              top: '50%'
            }}
            initial={{
              x: x,
              y: y,
              scale: 0.6
            }}
            animate={{
              x: isActive || isHovered ? 400 : x, // Move to right when active or hovered
              y: isActive || isHovered ? 0 : y,
              scale: isActive || isHovered ? 1.2 : 0.6,
              rotate: isAnimating && index === activePersona ? 360 : 0
            }}
            transition={{
              duration: 1,
              ease: "easeInOut",
              type: "spring",
              stiffness: 100
            }}
            onHoverStart={() => setHoveredPersona(persona.id)}
            onHoverEnd={() => setHoveredPersona(null)}
            onClick={() => handlePersonaClick(persona)}
            className="cursor-pointer"
          >
            <div className={`
              persona-bubble
              ${isActive || isHovered ? 'w-80 h-80' : 'w-24 h-24'}
              rounded-full bg-gradient-to-br ${persona.color}
              flex flex-col items-center justify-center text-white
              shadow-lg hover:shadow-2xl transition-all duration-500
              border-4 border-white/30
              ${gameMode ? 'ring-2 ring-yellow-400/50' : ''}
              ${isActive ? 'active-glow' : ''}
            `}>
              <motion.div
                className={`${isActive || isHovered ? 'text-6xl mb-4' : 'text-2xl'} transition-all duration-500`}
                animate={{
                  rotate: isHovered ? 360 : 0
                }}
                transition={{ duration: 0.8 }}
              >
                {persona.icon}
              </motion.div>

              {(isActive || isHovered) && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center px-6"
                >
                  <h3 className="text-2xl font-bold mb-3">
                    {persona.title}
                  </h3>
                  <p className="text-lg opacity-90 leading-relaxed">
                    {persona.description}
                  </p>
                </motion.div>
              )}

              {!(isActive || isHovered) && (
                <motion.p
                  className="text-xs text-center mt-1 opacity-80"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  {persona.shortDesc}
                </motion.p>
              )}
            </div>

            {/* Particle effects for hover */}
            <AnimatePresence>
              {isHovered && (
                <div className="absolute inset-0 pointer-events-none">
                  {particles.map((particle, i) => (
                    <motion.div
                      key={particle.id}
                      initial={{
                        scale: 0,
                        x: 0,
                        y: 0,
                        opacity: 1
                      }}
                      animate={{
                        scale: [0, 1, 0],
                        x: particle.x - 50,
                        y: particle.y - 50,
                        opacity: [1, 1, 0]
                      }}
                      transition={{
                        duration: 2,
                        delay: i * 0.1,
                        ease: "easeOut"
                      }}
                      className="absolute top-1/2 left-1/2 text-3xl"
                    >
                      {particle.emoji}
                    </motion.div>
                  ))}
                </div>
              )}
            </AnimatePresence>

            {/* Orbital trail effect */}
            {isActive && (
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-dashed border-white/30"
                animate={{ rotate: 360 }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
            )}
          </motion.div>
        );
      })}

      {/* Left side small balls showing other personas */}
      <div className="absolute left-20 top-1/2 transform -translate-y-1/2 space-y-8">
        {personas.map((persona, index) => {
          if (index === activePersona) return null;

          return (
            <motion.div
              key={`left-${persona.id}`}
              className="w-16 h-16 rounded-full bg-gradient-to-br from-gray-600 to-gray-800 flex items-center justify-center text-white shadow-lg cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={() => setActivePersona(index)}
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 0.7 }}
              transition={{ delay: index * 0.1 }}
            >
              <span className="text-2xl">{persona.icon}</span>
            </motion.div>
          );
        })}
      </div>

      {/* Game mode indicator */}
      {gameMode && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute top-8 right-8 bg-yellow-400/20 backdrop-blur-sm rounded-full px-4 py-2 text-yellow-400 font-mono text-sm"
        >
          🎮 Game Mode Active
        </motion.div>
      )}
    </div>
  );
}

export default PersonaBubbles;
