'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useState, useEffect } from 'react';

interface PersonaBubblesProps {
  gameMode: boolean;
  onDiscoverElement: (elementId: string) => void;
  discoveredElements: string[];
}

const personas = [
  {
    id: 'it-support',
    icon: '💻',
    title: 'IT Support Specialist',
    description: 'Troubleshooting networks and solving real-world problems',
    href: '/projects',
    color: 'from-blue-500 to-cyan-500',
    hoverAnimation: 'gear-spin',
    secretIcon: '⚙️',
    secretMessage: 'SYSTEM.ACCESS.GRANTED',
    particles: ['🔧', '🖥️', '🔌', '📡']
  },
  {
    id: 'automation',
    icon: '🤖',
    title: 'Automation Engineer',
    description: 'Building scripts and workflows that make life easier',
    href: '/about',
    color: 'from-purple-500 to-pink-500',
    hoverAnimation: 'terminal-effect',
    secretIcon: '⚡',
    secretMessage: 'AUTOMATION.PROTOCOL.ACTIVE',
    particles: ['⚡', '🔄', '📊', '🎯']
  },
  {
    id: 'creative',
    icon: '🎨',
    title: 'Creative Technologist',
    description: 'Where art meets technology and innovation happens',
    href: '/about',
    color: 'from-orange-500 to-red-500',
    hoverAnimation: 'paintbrush-stroke',
    secretIcon: '✨',
    secretMessage: 'CREATIVITY.MODE.ENABLED',
    particles: ['🎨', '✨', '🌈', '💫']
  }
];

export function PersonaBubbles({ gameMode, onDiscoverElement, discoveredElements }: PersonaBubblesProps) {
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const [clickCounts, setClickCounts] = useState<Record<string, number>>({});
  const [showParticles, setShowParticles] = useState<Record<string, boolean>>({});
  const [secretMode, setSecretMode] = useState<Record<string, boolean>>({});

  // Handle multiple clicks to unlock secret modes
  const handleBubbleClick = (persona: typeof personas[0], e: React.MouseEvent) => {
    e.preventDefault();

    const newCount = (clickCounts[persona.id] || 0) + 1;
    setClickCounts(prev => ({ ...prev, [persona.id]: newCount }));

    // Trigger particles on each click
    setShowParticles(prev => ({ ...prev, [persona.id]: true }));
    setTimeout(() => {
      setShowParticles(prev => ({ ...prev, [persona.id]: false }));
    }, 1000);

    // Unlock secret mode after 5 clicks
    if (newCount >= 5) {
      setSecretMode(prev => ({ ...prev, [persona.id]: true }));
      onDiscoverElement(`persona-secret-${persona.id}`);
    }

    // Navigate after a short delay to show effects
    setTimeout(() => {
      window.location.href = persona.href;
    }, 300);
  };

  const handleLongPress = (persona: typeof personas[0]) => {
    onDiscoverElement(`persona-longpress-${persona.id}`);
    setSecretMode(prev => ({ ...prev, [persona.id]: true }));
  };

  // Long press detection
  useEffect(() => {
    let pressTimer: NodeJS.Timeout;

    const startPress = (personaId: string) => {
      pressTimer = setTimeout(() => {
        const persona = personas.find(p => p.id === personaId);
        if (persona) handleLongPress(persona);
      }, 2000);
    };

    const endPress = () => {
      clearTimeout(pressTimer);
    };

    return () => clearTimeout(pressTimer);
  }, []);

  return (
    <div className="flex flex-col md:flex-row gap-8 justify-center items-center relative">
      {personas.map((persona, index) => (
        <motion.div
          key={persona.id}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.2 }}
          whileHover={{ scale: gameMode ? 1.1 : 1.05 }}
          whileTap={{ scale: 0.9 }}
          onHoverStart={() => {
            setHoveredId(persona.id);
            if (gameMode) onDiscoverElement(`persona-hover-${persona.id}`);
          }}
          onHoverEnd={() => setHoveredId(null)}
          className="relative"
        >
          <div
            className="relative group cursor-pointer"
            onClick={(e) => handleBubbleClick(persona, e)}
            onMouseDown={() => {
              // Start long press timer
            }}
            onMouseUp={() => {
              // Clear long press timer
            }}
          >
            <div className={`
              w-48 h-48 rounded-full bg-gradient-to-br ${persona.color}
              flex flex-col items-center justify-center text-white
              shadow-lg hover:shadow-2xl transition-all duration-300
              border-4 border-white/20 hover:border-white/40
              ${gameMode ? 'animate-pulse' : ''}
              ${secretMode[persona.id] ? 'ring-4 ring-yellow-400 ring-opacity-75' : ''}
            `}>
              <motion.div
                animate={hoveredId === persona.id ? {
                  rotate: persona.hoverAnimation === 'gear-spin' ? 360 : 0,
                  scale: persona.hoverAnimation === 'terminal-effect' ? [1, 1.1, 1] : 1
                } : {}}
                transition={{ duration: 0.8, ease: "easeInOut" }}
                className="text-4xl mb-3 relative"
              >
                {secretMode[persona.id] ? persona.secretIcon : persona.icon}

                {/* Click counter */}
                {gameMode && clickCounts[persona.id] > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 bg-yellow-400 text-black rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold"
                  >
                    {clickCounts[persona.id]}
                  </motion.div>
                )}
              </motion.div>

              <h3 className="text-lg font-bold text-center px-4 mb-2">
                {persona.title}
              </h3>

              <p className="text-sm text-center px-4 opacity-90">
                {secretMode[persona.id] ? persona.secretMessage : persona.description}
              </p>
            </div>

            {/* Particle explosion effect */}
            <AnimatePresence>
              {showParticles[persona.id] && (
                <div className="absolute inset-0 pointer-events-none">
                  {persona.particles.map((particle, i) => (
                    <motion.div
                      key={i}
                      initial={{
                        scale: 0,
                        x: 0,
                        y: 0,
                        opacity: 1
                      }}
                      animate={{
                        scale: [0, 1, 0],
                        x: (Math.random() - 0.5) * 200,
                        y: (Math.random() - 0.5) * 200,
                        opacity: [1, 1, 0]
                      }}
                      transition={{
                        duration: 1,
                        delay: i * 0.1,
                        ease: "easeOut"
                      }}
                      className="absolute top-1/2 left-1/2 text-2xl"
                    >
                      {particle}
                    </motion.div>
                  ))}
                </div>
              )}
            </AnimatePresence>

            {/* Hover effect overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: hoveredId === persona.id ? 1 : 0 }}
              className="absolute inset-0 rounded-full bg-white/10 backdrop-blur-sm"
            />

            {/* Animated border on hover */}
            <motion.div
              initial={{ scale: 1, opacity: 0 }}
              animate={{
                scale: hoveredId === persona.id ? 1.1 : 1,
                opacity: hoveredId === persona.id ? 1 : 0
              }}
              className={`
                absolute inset-0 rounded-full border-2 border-dashed
                ${persona.color.includes('blue') ? 'border-blue-300' :
                  persona.color.includes('purple') ? 'border-purple-300' :
                  'border-orange-300'}
                animate-spin
              `}
              style={{ animationDuration: '3s' }}
            />

            {/* Secret mode glow effect */}
            <AnimatePresence>
              {secretMode[persona.id] && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1.2 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="absolute inset-0 rounded-full bg-yellow-400/20 animate-pulse"
                />
              )}
            </AnimatePresence>

            {/* Game mode indicators */}
            {gameMode && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground font-mono"
              >
                {clickCounts[persona.id] >= 5 ? '🔓 UNLOCKED' :
                 clickCounts[persona.id] > 0 ? `${5 - clickCounts[persona.id]} clicks to unlock` :
                 'Click 5 times to unlock'}
              </motion.div>
            )}
          </div>
        </motion.div>
      ))}

      {/* Secret combination detector */}
      {gameMode && Object.values(secretMode).filter(Boolean).length === 3 && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-full font-bold text-lg shadow-lg"
          onClick={() => onDiscoverElement('all-personas-unlocked')}
        >
          🎉 ALL PERSONAS UNLOCKED! 🎉
        </motion.div>
      )}
    </div>
  );
}
