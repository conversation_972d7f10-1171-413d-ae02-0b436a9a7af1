'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';

const personas = [
  {
    id: 'it-support',
    icon: '💻',
    title: 'IT Support Specialist',
    description: 'Troubleshooting networks and solving real-world problems',
    href: '/projects',
    color: 'from-blue-500 to-cyan-500',
    hoverAnimation: 'gear-spin'
  },
  {
    id: 'automation',
    icon: '🤖',
    title: 'Automation Engineer',
    description: 'Building scripts and workflows that make life easier',
    href: '/about',
    color: 'from-purple-500 to-pink-500',
    hoverAnimation: 'terminal-effect'
  },
  {
    id: 'creative',
    icon: '🎨',
    title: 'Creative Technologist',
    description: 'Where art meets technology and innovation happens',
    href: '/about',
    color: 'from-orange-500 to-red-500',
    hoverAnimation: 'paintbrush-stroke'
  }
];

export function PersonaBubbles() {
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  return (
    <div className="flex flex-col md:flex-row gap-8 justify-center items-center">
      {personas.map((persona, index) => (
        <motion.div
          key={persona.id}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.2 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onHoverStart={() => setHoveredId(persona.id)}
          onHoverEnd={() => setHoveredId(null)}
        >
          <Link href={persona.href}>
            <div className="relative group cursor-pointer">
              <div className={`
                w-48 h-48 rounded-full bg-gradient-to-br ${persona.color}
                flex flex-col items-center justify-center text-white
                shadow-lg hover:shadow-2xl transition-all duration-300
                border-4 border-white/20 hover:border-white/40
              `}>
                <motion.div
                  animate={hoveredId === persona.id ? {
                    rotate: persona.hoverAnimation === 'gear-spin' ? 360 : 0,
                    scale: persona.hoverAnimation === 'terminal-effect' ? [1, 1.1, 1] : 1
                  } : {}}
                  transition={{ duration: 0.8, ease: "easeInOut" }}
                  className="text-4xl mb-3"
                >
                  {persona.icon}
                </motion.div>
                
                <h3 className="text-lg font-bold text-center px-4 mb-2">
                  {persona.title}
                </h3>
                
                <p className="text-sm text-center px-4 opacity-90">
                  {persona.description}
                </p>
              </div>

              {/* Hover effect overlay */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: hoveredId === persona.id ? 1 : 0 }}
                className="absolute inset-0 rounded-full bg-white/10 backdrop-blur-sm"
              />

              {/* Animated border on hover */}
              <motion.div
                initial={{ scale: 1, opacity: 0 }}
                animate={{ 
                  scale: hoveredId === persona.id ? 1.1 : 1,
                  opacity: hoveredId === persona.id ? 1 : 0
                }}
                className={`
                  absolute inset-0 rounded-full border-2 border-dashed
                  ${persona.color.includes('blue') ? 'border-blue-300' : 
                    persona.color.includes('purple') ? 'border-purple-300' : 
                    'border-orange-300'}
                  animate-spin
                `}
                style={{ animationDuration: '3s' }}
              />
            </div>
          </Link>
        </motion.div>
      ))}
    </div>
  );
}
