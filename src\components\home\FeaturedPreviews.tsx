'use client';

import { motion, useInView, AnimatePresence } from 'framer-motion';
import { useRef, useState } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';

const previews = [
  {
    id: 'projects',
    title: 'Projects',
    emoji: '✨',
    description: 'Technical skills through project stories',
    href: '/projects',
    preview: 'Featured: Network Automation & SIEM Implementation',
    color: 'from-blue-500/20 to-cyan-500/20',
    borderColor: 'border-blue-500/30',
    secretEmoji: '🔧',
    secretPreview: 'Hidden: AI-powered troubleshooting bot',
    interactiveElements: ['💻', '🔌', '📡', '⚙️']
  },
  {
    id: 'professors',
    title: 'Professors',
    emoji: '👩‍🏫',
    description: 'Mentors who shaped my journey',
    href: '/professors',
    preview: 'Dr. <PERSON> • Prof. Johnson • Ms. Chen',
    color: 'from-green-500/20 to-emerald-500/20',
    borderColor: 'border-green-500/30',
    secretEmoji: '🎓',
    secretPreview: 'Secret: The professor who changed everything',
    interactiveElements: ['📚', '🎓', '✏️', '🧠']
  },
  {
    id: 'game',
    title: 'Game',
    emoji: '🎮',
    description: 'Side-scrolling adventure in development',
    href: '/game',
    preview: 'Coming Soon: Pixel-art platformer',
    color: 'from-purple-500/20 to-pink-500/20',
    borderColor: 'border-purple-500/30',
    secretEmoji: '👾',
    secretPreview: 'Easter Egg: Play the mini-game now!',
    interactiveElements: ['🎮', '👾', '🕹️', '🎯']
  },
  {
    id: 'about',
    title: 'About Me',
    emoji: '👤',
    description: 'My story, values, and aspirations',
    href: '/about',
    preview: '"I troubleshoot networks and paint pixel art"',
    color: 'from-orange-500/20 to-red-500/20',
    borderColor: 'border-orange-500/30',
    secretEmoji: '🎨',
    secretPreview: 'Hidden talent: Digital art gallery',
    interactiveElements: ['🎨', '✨', '🌈', '💫']
  },
  {
    id: 'contact',
    title: 'Contact',
    emoji: '📬',
    description: 'Ready to connect and collaborate',
    href: '/contact',
    preview: 'Let\'s build something amazing together!',
    color: 'from-teal-500/20 to-blue-500/20',
    borderColor: 'border-teal-500/30',
    secretEmoji: '🚀',
    secretPreview: 'Direct line: Instant message portal',
    interactiveElements: ['📬', '💌', '🚀', '⚡']
  }
];

export function FeaturedPreviews() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="py-24 px-6">
      <div className="container mx-auto max-w-6xl">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Explore My World
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover the different facets of my journey in technology, education, and creativity
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {previews.map((preview, index) => (
            <PreviewCard
              key={preview.id}
              preview={preview}
              index={index}
              isInView={isInView}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

interface PreviewCardProps {
  preview: typeof previews[0];
  index: number;
  isInView: boolean;
}

function PreviewCard({ preview, index, isInView }: PreviewCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const [clickCount, setClickCount] = useState(0);
  const [showParticles, setShowParticles] = useState(false);
  const [secretUnlocked, setSecretUnlocked] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    e.preventDefault();

    const newClickCount = clickCount + 1;
    setClickCount(newClickCount);

    // Flip card on double click
    if (newClickCount % 2 === 0) {
      setIsFlipped(!isFlipped);
    }

    // Unlock secret after 5 clicks
    if (newClickCount >= 5) {
      setSecretUnlocked(true);
      setShowParticles(true);
      setTimeout(() => setShowParticles(false), 2000);
    }

    // Navigate after showing effects
    setTimeout(() => {
      window.location.href = preview.href;
    }, isFlipped ? 600 : 200);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateX: -15 }}
      animate={isInView ? {
        opacity: 1,
        y: 0,
        rotateX: 0
      } : {
        opacity: 0,
        y: 50,
        rotateX: -15
      }}
      transition={{
        duration: 0.8,
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{
        scale: 1.05,
        rotateY: 5,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.95 }}
      className="perspective-1000 relative"
    >
      <div
        className="relative preserve-3d cursor-pointer"
        style={{
          transformStyle: 'preserve-3d',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
          transition: 'transform 0.6s'
        }}
        onClick={handleCardClick}
      >
        {/* Front of card */}
        <Card className={`
          h-full transition-all duration-300 backface-hidden
          bg-gradient-to-br ${preview.color}
          border-2 ${preview.borderColor}
          hover:shadow-2xl hover:shadow-primary/20
          group overflow-hidden
          ${secretUnlocked ? 'ring-2 ring-yellow-400 ring-opacity-75' : ''}
        `}>
          <CardContent className="p-8 h-full flex flex-col justify-between relative">
            {/* Interactive floating elements */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {preview.interactiveElements.map((element, i) => (
                <motion.div
                  key={i}
                  className="absolute text-lg opacity-20"
                  style={{
                    left: `${20 + i * 20}%`,
                    top: `${10 + i * 15}%`,
                  }}
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, 5, -5, 0],
                    opacity: [0.2, 0.4, 0.2]
                  }}
                  transition={{
                    duration: 3 + i,
                    repeat: Infinity,
                    delay: i * 0.5
                  }}
                >
                  {element}
                </motion.div>
              ))}
            </div>

            <div className="relative z-10">
              <motion.div
                whileHover={{
                  scale: 1.2,
                  rotate: [0, -10, 10, 0],
                  transition: { duration: 0.5 }
                }}
                className="text-4xl mb-4 inline-block relative"
              >
                {secretUnlocked ? preview.secretEmoji : preview.emoji}

                {/* Click counter */}
                {clickCount > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold"
                  >
                    {clickCount}
                  </motion.div>
                )}
              </motion.div>

              <h3 className="text-2xl font-bold mb-3 group-hover:text-primary transition-colors">
                {preview.title}
              </h3>

              <p className="text-muted-foreground mb-4">
                {preview.description}
              </p>
            </div>

            <div className="mt-auto relative z-10">
              <motion.div
                initial={{ opacity: 0.7 }}
                whileHover={{ opacity: 1 }}
                className="text-sm font-medium text-primary/80 group-hover:text-primary transition-colors"
              >
                {secretUnlocked ? preview.secretPreview : preview.preview}
              </motion.div>

              <motion.div
                className="flex items-center mt-3 text-sm text-muted-foreground group-hover:text-primary transition-colors"
                whileHover={{ x: 5 }}
              >
                <span>{isFlipped ? 'Enter Portal' : 'Explore'}</span>
                <motion.svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  whileHover={{ x: 3 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </motion.svg>
              </motion.div>

              {/* Hint text */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: clickCount > 0 ? 1 : 0 }}
                className="text-xs text-muted-foreground/60 mt-2"
              >
                {clickCount < 5 ? `${5 - clickCount} more clicks to unlock secret` : '🔓 Secret unlocked!'}
              </motion.div>
            </div>
          </CardContent>
        </Card>

        {/* Back of card (flipped view) */}
        <Card className={`
          absolute inset-0 h-full transition-all duration-300
          bg-gradient-to-br from-purple-600/20 to-pink-600/20
          border-2 border-purple-500/30
          backface-hidden
        `} style={{ transform: 'rotateY(180deg)' }}>
          <CardContent className="p-8 h-full flex flex-col justify-center items-center text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="text-6xl mb-4"
            >
              🌀
            </motion.div>
            <h3 className="text-xl font-bold mb-4 text-primary">
              Secret Portal Activated
            </h3>
            <p className="text-muted-foreground mb-6">
              You've discovered a hidden pathway to {preview.title}
            </p>
            <div className="text-sm font-mono text-primary/80">
              ACCESSING ENHANCED MODE...
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Particle explosion effect */}
      <AnimatePresence>
        {showParticles && (
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 12 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  scale: 0,
                  x: 0,
                  y: 0,
                  opacity: 1
                }}
                animate={{
                  scale: [0, 1, 0],
                  x: (Math.random() - 0.5) * 300,
                  y: (Math.random() - 0.5) * 300,
                  opacity: [1, 1, 0]
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.1,
                  ease: "easeOut"
                }}
                className="absolute top-1/2 left-1/2 text-2xl"
              >
                {preview.interactiveElements[i % preview.interactiveElements.length]}
              </motion.div>
            ))}
          </div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
