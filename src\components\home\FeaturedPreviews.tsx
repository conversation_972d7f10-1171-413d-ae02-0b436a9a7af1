'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';

const previews = [
  {
    id: 'projects',
    title: 'Projects',
    emoji: '✨',
    description: 'Technical skills through project stories',
    href: '/projects',
    preview: 'Featured: Network Automation & SIEM Implementation',
    color: 'from-blue-500/20 to-cyan-500/20',
    borderColor: 'border-blue-500/30'
  },
  {
    id: 'professors',
    title: 'Professors',
    emoji: '👩‍🏫',
    description: 'Mentors who shaped my journey',
    href: '/professors',
    preview: '<PERSON><PERSON> • Prof<PERSON> • Ms. <PERSON>',
    color: 'from-green-500/20 to-emerald-500/20',
    borderColor: 'border-green-500/30'
  },
  {
    id: 'game',
    title: 'Game',
    emoji: '🎮',
    description: 'Side-scrolling adventure in development',
    href: '/game',
    preview: 'Coming Soon: Pixel-art platformer',
    color: 'from-purple-500/20 to-pink-500/20',
    borderColor: 'border-purple-500/30'
  },
  {
    id: 'about',
    title: 'About Me',
    emoji: '👤',
    description: 'My story, values, and aspirations',
    href: '/about',
    preview: '"I troubleshoot networks and paint pixel art"',
    color: 'from-orange-500/20 to-red-500/20',
    borderColor: 'border-orange-500/30'
  },
  {
    id: 'contact',
    title: 'Contact',
    emoji: '📬',
    description: 'Ready to connect and collaborate',
    href: '/contact',
    preview: 'Let\'s build something amazing together!',
    color: 'from-teal-500/20 to-blue-500/20',
    borderColor: 'border-teal-500/30'
  }
];

export function FeaturedPreviews() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="py-24 px-6">
      <div className="container mx-auto max-w-6xl">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Explore My World
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover the different facets of my journey in technology, education, and creativity
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {previews.map((preview, index) => (
            <PreviewCard
              key={preview.id}
              preview={preview}
              index={index}
              isInView={isInView}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

interface PreviewCardProps {
  preview: typeof previews[0];
  index: number;
  isInView: boolean;
}

function PreviewCard({ preview, index, isInView }: PreviewCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateX: -15 }}
      animate={isInView ? { 
        opacity: 1, 
        y: 0, 
        rotateX: 0 
      } : { 
        opacity: 0, 
        y: 50, 
        rotateX: -15 
      }}
      transition={{ 
        duration: 0.8, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        scale: 1.05, 
        rotateY: 5,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.95 }}
      className="perspective-1000"
    >
      <Link href={preview.href}>
        <Card className={`
          h-full cursor-pointer transition-all duration-300
          bg-gradient-to-br ${preview.color}
          border-2 ${preview.borderColor}
          hover:shadow-2xl hover:shadow-primary/20
          group overflow-hidden
        `}>
          <CardContent className="p-8 h-full flex flex-col justify-between">
            <div>
              <motion.div
                whileHover={{ 
                  scale: 1.2, 
                  rotate: [0, -10, 10, 0],
                  transition: { duration: 0.5 }
                }}
                className="text-4xl mb-4 inline-block"
              >
                {preview.emoji}
              </motion.div>
              
              <h3 className="text-2xl font-bold mb-3 group-hover:text-primary transition-colors">
                {preview.title}
              </h3>
              
              <p className="text-muted-foreground mb-4">
                {preview.description}
              </p>
            </div>
            
            <div className="mt-auto">
              <motion.div
                initial={{ opacity: 0.7 }}
                whileHover={{ opacity: 1 }}
                className="text-sm font-medium text-primary/80 group-hover:text-primary transition-colors"
              >
                {preview.preview}
              </motion.div>
              
              <motion.div
                className="flex items-center mt-3 text-sm text-muted-foreground group-hover:text-primary transition-colors"
                whileHover={{ x: 5 }}
              >
                <span>Explore</span>
                <motion.svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  whileHover={{ x: 3 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </motion.svg>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </Link>
    </motion.div>
  );
}
