'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  type: 'code' | 'data' | 'neural' | 'paint';
  content: string;
  color: string;
}

const codeSnippets = [
  'console.log("Hello World")',
  'function solve()',
  'import React',
  'const data = []',
  'if (condition)',
  'return true',
  'async/await',
  'npm install'
];

const dataFlows = ['→', '↗', '↘', '←', '↙', '↖', '↑', '↓'];
const neuralNodes = ['●', '◐', '◑', '◒', '◓', '○'];
const paintStrokes = ['~', '∼', '≈', '⌒', '⌐', '¬'];

export function BackgroundAnimation() {
  const [elements, setElements] = useState<FloatingElement[]>([]);

  useEffect(() => {
    const generateElements = () => {
      const newElements: FloatingElement[] = [];
      
      // Generate floating elements
      for (let i = 0; i < 30; i++) {
        const types: FloatingElement['type'][] = ['code', 'data', 'neural', 'paint'];
        const type = types[Math.floor(Math.random() * types.length)];
        
        let content = '';
        let color = '';
        
        switch (type) {
          case 'code':
            content = codeSnippets[Math.floor(Math.random() * codeSnippets.length)];
            color = 'text-blue-400/30';
            break;
          case 'data':
            content = dataFlows[Math.floor(Math.random() * dataFlows.length)];
            color = 'text-green-400/30';
            break;
          case 'neural':
            content = neuralNodes[Math.floor(Math.random() * neuralNodes.length)];
            color = 'text-purple-400/30';
            break;
          case 'paint':
            content = paintStrokes[Math.floor(Math.random() * paintStrokes.length)];
            color = 'text-orange-400/30';
            break;
        }
        
        newElements.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 20 + 10,
          type,
          content,
          color
        });
      }
      
      setElements(newElements);
    };

    generateElements();
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background/90" />
      
      {/* Floating elements */}
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className={`absolute ${element.color} font-mono select-none`}
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            fontSize: `${element.size}px`,
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ 
            opacity: [0, 1, 1, 0],
            scale: [0, 1, 1, 0],
            x: [0, Math.random() * 100 - 50],
            y: [0, Math.random() * 100 - 50],
            rotate: [0, Math.random() * 360]
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Infinity,
            repeatType: 'loop',
            ease: 'linear',
            delay: Math.random() * 5
          }}
        >
          {element.content}
        </motion.div>
      ))}
      
      {/* Neural network webbing */}
      <svg className="absolute inset-0 w-full h-full opacity-10">
        <defs>
          <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
            <path d="M 100 0 L 0 0 0 100" fill="none" stroke="currentColor" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* Animated connecting lines */}
        {Array.from({ length: 5 }).map((_, i) => (
          <motion.line
            key={i}
            x1={`${Math.random() * 100}%`}
            y1={`${Math.random() * 100}%`}
            x2={`${Math.random() * 100}%`}
            y2={`${Math.random() * 100}%`}
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: 'easeInOut',
              delay: Math.random() * 2
            }}
          />
        ))}
      </svg>
      
      {/* Parallax layers */}
      <motion.div
        className="absolute inset-0 opacity-5"
        animate={{ y: [0, -20, 0] }}
        transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
      >
        <div className="w-full h-full bg-gradient-to-r from-transparent via-primary/10 to-transparent" />
      </motion.div>
    </div>
  );
}
